import React, { useEffect, useState } from 'react';
import {
  API,
  showError,
  showSuccess,
  timestamp2string,
} from '../../helpers';
import { ITEMS_PER_PAGE } from '../../constants';
import {
  Button,
  Card,
  Divider,
  Empty,
  Form,
  Modal,
  Space,
  Table,
  Tag,
  Switch,
  Input,
  Typography
} from '@douyinfe/semi-ui';
import {
  IllustrationNoResult,
  IllustrationNoResultDark
} from '@douyinfe/semi-illustrations';
import {
  IconSearch,
} from '@douyinfe/semi-icons';
import { Settings } from 'lucide-react';
import EditAutoModel from '../../pages/AutoModel/EditAutoModel';
import { useTranslation } from 'react-i18next';
import { useTableCompactMode } from '../../hooks/useTableCompactMode';

const { Text } = Typography;

function renderTimestamp(timestamp) {
  return <>{timestamp2string(timestamp)}</>;
}

const AutoModelsTable = () => {
  const { t } = useTranslation();

  const columns = [
    {
      title: t('名称'),
      dataIndex: 'name',
    },
    {
      title: t('描述'),
      dataIndex: 'description',
      render: (text) => text || t('无描述'),
    },
    {
      title: t('状态'),
      dataIndex: 'is_active',
      key: 'is_active',
      render: (text, record) => {
        const enabled = text === true;
        const handleToggle = (checked) => {
          if (checked) {
            manageAutoModel(record.id, 'enable', record);
          } else {
            manageAutoModel(record.id, 'disable', record);
          }
        };

        let tagColor = enabled ? 'green' : 'red';
        let tagText = enabled ? t('已启用') : t('已禁用');

        return (
          <Tag
            color={tagColor}
            shape='circle'
            size='large'
            prefixIcon={
              <Switch
                size='small'
                checked={enabled}
                onChange={handleToggle}
                aria-label='automodel status switch'
              />
            }
          >
            {tagText}
          </Tag>
        );
      },
    },
    {
      title: t('模型列表'),
      dataIndex: 'models',
      render: (models) => {
        if (!models || models.length === 0) {
          return <Tag color='grey'>{t('无模型')}</Tag>;
        }
        
        const displayModels = models.slice(0, 2);
        const extraCount = models.length - displayModels.length;
        
        return (
          <div className="flex flex-wrap gap-1">
            {displayModels.map((model, idx) => (
              <Tag key={idx} size='small'>{model}</Tag>
            ))}
            {extraCount > 0 && (
              <Tag size='small' color='grey'>+{extraCount}</Tag>
            )}
          </div>
        );
      },
    },
    {
      title: t('创建时间'),
      dataIndex: 'created_at',
      render: renderTimestamp,
    },
    {
      title: t('更新时间'),
      dataIndex: 'updated_at',
      render: renderTimestamp,
    },
    {
      title: t('操作'),
      dataIndex: 'operate',
      key: 'operate',
      fixed: 'right',
      render: (text, record) => (
        <Space>
          <Button
            type='tertiary'
            size="small"
            onClick={() => {
              setEditingAutoModel(record);
              setShowEdit(true);
            }}
          >
            {t('编辑')}
          </Button>

          <Button
            type='danger'
            size="small"
            onClick={() => {
              Modal.confirm({
                title: t('确定是否要删除此 Auto Model？'),
                content: t('此修改将不可逆'),
                onOk: () => {
                  (async () => {
                    await manageAutoModel(record.id, 'delete', record);
                    await refresh();
                  })();
                },
              });
            }}
          >
            {t('删除')}
          </Button>
        </Space>
      ),
    },
  ];

  const [pageSize, setPageSize] = useState(ITEMS_PER_PAGE);
  const [showEdit, setShowEdit] = useState(false);
  const [autoModels, setAutoModels] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [autoModelCount, setAutoModelCount] = useState(pageSize);
  const [loading, setLoading] = useState(true);
  const [activePage, setActivePage] = useState(1);
  const [searching, setSearching] = useState(false);
  const [editingAutoModel, setEditingAutoModel] = useState({
    id: undefined,
  });
  const [compactMode, setCompactMode] = useTableCompactMode('automodels');

  // Form 初始值
  const formInitValues = {
    searchKeyword: '',
  };

  // Form API 引用
  const [formApi, setFormApi] = useState(null);

  // 获取表单值的辅助函数
  const getFormValues = () => {
    const formValues = formApi ? formApi.getValues() : {};
    return {
      searchKeyword: formValues.searchKeyword || '',
    };
  };

  const closeEdit = () => {
    setShowEdit(false);
    setTimeout(() => {
      setEditingAutoModel({
        id: undefined,
      });
    }, 500);
  };

  // 将后端返回的数据写入状态
  const syncPageData = (payload) => {
    setAutoModels(payload || []);
    setAutoModelCount(payload?.length || 0);
    setActivePage(1);
  };

  const loadAutoModels = async () => {
    setLoading(true);
    try {
      const res = await API.get('/api/auto_models');
      const { success, message, data } = res.data;
      if (success) {
        // Data already contains models field directly from the API
        syncPageData(data);
      } else {
        showError(message);
      }
    } catch (error) {
      showError(error.message);
    }
    setLoading(false);
  };

  const refresh = async () => {
    await loadAutoModels();
    setSelectedKeys([]);
  };

  useEffect(() => {
    loadAutoModels()
      .then()
      .catch((reason) => {
        showError(reason);
      });
  }, []);

  const manageAutoModel = async (id, action, record) => {
    setLoading(true);
    let res;
    try {
      switch (action) {
        case 'delete':
          res = await API.delete(`/api/auto_models/${record.name}`);
          break;
        case 'enable':
          res = await API.put(`/api/auto_models/${record.name}`, {
            ...record,
            is_active: true,
            models: record.models || []
          });
          break;
        case 'disable':
          res = await API.put(`/api/auto_models/${record.name}`, {
            ...record,
            is_active: false,
            models: record.models || []
          });
          break;
      }
      const { success, message } = res.data;
      if (success) {
        showSuccess('操作成功完成！');
        if (action !== 'delete') {
          // 更新本地状态
          const newAutoModels = [...autoModels];
          const index = newAutoModels.findIndex(item => item.id === id);
          if (index !== -1) {
            newAutoModels[index].is_active = action === 'enable';
            setAutoModels(newAutoModels);
          }
        } else {
          await refresh();
        }
      } else {
        showError(message);
      }
    } catch (error) {
      showError(error.message);
    }
    setLoading(false);
  };

  const searchAutoModels = async () => {
    const { searchKeyword } = getFormValues();
    if (searchKeyword === '') {
      await loadAutoModels();
      return;
    }
    setSearching(true);
    try {
      const res = await API.get('/api/auto_models');
      const { success, message, data } = res.data;
      if (success) {
        // 过滤数据
        const filteredData = data.filter(item =>
          item.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
          (item.description && item.description.toLowerCase().includes(searchKeyword.toLowerCase()))
        );
        // Data already contains models field directly from the API
        syncPageData(filteredData);
      } else {
        showError(message);
      }
    } catch (error) {
      showError(error.message);
    }
    setSearching(false);
  };

  const rowSelection = {
    onSelect: (record, selected) => { },
    onSelectAll: (selected, selectedRows) => { },
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedKeys(selectedRows);
    },
  };

  const handleRow = (record, index) => {
    if (!record.is_active) {
      return {
        style: {
          background: 'var(--semi-color-disabled-border)',
        },
      };
    } else {
      return {};
    }
  };

  const batchDeleteAutoModels = async () => {
    if (selectedKeys.length === 0) {
      showError(t('请先选择要删除的 Auto Model！'));
      return;
    }
    setLoading(true);
    try {
      let successCount = 0;
      for (const autoModel of selectedKeys) {
        try {
          const res = await API.delete(`/api/auto_models/${autoModel.name}`);
          if (res?.data?.success) {
            successCount++;
          }
        } catch (error) {
          console.error(`Failed to delete ${autoModel.name}:`, error);
        }
      }
      if (successCount > 0) {
        showSuccess(t('已删除 {{count}} 个 Auto Model！', { count: successCount }));
        await refresh();
      } else {
        showError(t('删除失败'));
      }
    } catch (error) {
      showError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const renderHeader = () => (
    <div className="flex flex-col w-full">
      <div className="mb-2">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-2 w-full">
          <div className="flex items-center text-blue-500">
            <Settings size={16} className="mr-2" />
            <Text>{t('Auto Model 用于配置自动模型选择策略，可以设置模型优先级和启用状态。')}</Text>
          </div>
          <Button
            type="tertiary"
            className="w-full md:w-auto"
            onClick={() => setCompactMode(!compactMode)}
            size="small"
          >
            {compactMode ? t('自适应列表') : t('紧凑列表')}
          </Button>
        </div>
      </div>

      <Divider margin="12px" />

      <div className="flex flex-col md:flex-row justify-between items-center gap-4 w-full">
        <div className="flex flex-wrap gap-2 w-full md:w-auto order-2 md:order-1">
          <Button
            type="primary"
            className="flex-1 md:flex-initial"
            onClick={() => {
              setEditingAutoModel({
                id: undefined,
              });
              setShowEdit(true);
            }}
            size="small"
          >
            {t('添加 Auto Model')}
          </Button>
          <Button
            type='danger'
            className="w-full md:w-auto"
            onClick={() => {
              if (selectedKeys.length === 0) {
                showError(t('请至少选择一个 Auto Model！'));
                return;
              }
              Modal.confirm({
                title: t('批量删除 Auto Model'),
                content: (
                  <div>
                    {t('确定要删除所选的 {{count}} 个 Auto Model 吗？', { count: selectedKeys.length })}
                  </div>
                ),
                onOk: () => batchDeleteAutoModels(),
              });
            }}
            size="small"
          >
            {t('删除所选 Auto Model')}
          </Button>
        </div>

        <Form
          initValues={formInitValues}
          getFormApi={(api) => setFormApi(api)}
          onSubmit={searchAutoModels}
          allowEmpty={true}
          autoComplete="off"
          layout="horizontal"
          trigger="change"
          stopValidateWithError={false}
          className="w-full md:w-auto order-1 md:order-2"
        >
          <div className="flex flex-col md:flex-row items-center gap-4 w-full md:w-auto">
            <div className="relative w-full md:w-56">
              <Form.Input
                field="searchKeyword"
                prefix={<IconSearch />}
                placeholder={t('搜索关键字')}
                showClear
                pure
                size="small"
              />
            </div>
            <div className="flex gap-2 w-full md:w-auto">
              <Button
                type="tertiary"
                htmlType="submit"
                loading={loading || searching}
                className="flex-1 md:flex-initial md:w-auto"
                size="small"
              >
                {t('查询')}
              </Button>
              <Button
                type='tertiary'
                onClick={() => {
                  if (formApi) {
                    formApi.reset();
                    // 重置后立即查询，使用setTimeout确保表单重置完成
                    setTimeout(() => {
                      searchAutoModels();
                    }, 100);
                  }
                }}
                className="flex-1 md:flex-initial md:w-auto"
                size="small"
              >
                {t('重置')}
              </Button>
            </div>
          </div>
        </Form>
      </div>
    </div>
  );

  return (
    <>
      <EditAutoModel
        refresh={refresh}
        editingAutoModel={editingAutoModel}
        visiable={showEdit}
        handleClose={closeEdit}
      ></EditAutoModel>

      <Card
        className="!rounded-2xl"
        title={renderHeader()}
        shadows='always'
        bordered={false}
      >
        <Table
          columns={compactMode ? columns.map(col => {
            if (col.dataIndex === 'operate') {
              const { fixed, ...rest } = col;
              return rest;
            }
            return col;
          }) : columns}
          dataSource={autoModels}
          scroll={compactMode ? undefined : { x: 'max-content' }}
          pagination={false}
          loading={loading}
          rowSelection={rowSelection}
          onRow={handleRow}
          empty={
            <Empty
              image={<IllustrationNoResult style={{ width: 150, height: 150 }} />}
              darkModeImage={<IllustrationNoResultDark style={{ width: 150, height: 150 }} />}
              description={t('暂无 Auto Model 配置')}
              style={{ padding: 30 }}
            />
          }
          className="rounded-xl overflow-hidden"
          size="middle"
        ></Table>
      </Card>
    </>
  );
};

export default AutoModelsTable;
